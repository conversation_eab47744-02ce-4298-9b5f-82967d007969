import asyncio
import aiohttp
import json
import os
import time
from urllib.parse import urljoin, urlparse, urlunparse
from typing import Set, List, Dict, Optional, Tuple
from dataclasses import dataclass, field
from collections import deque
import re
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class CrawlConfig:
    """Configuration for the web crawler"""
    max_pages: int = 50
    max_depth: int = 3
    delay_between_requests: float = 1.0
    timeout: int = 30
    retries: int = 3
    geo: str = "us"
    extractor_preset: str = "markdown"

@dataclass
class CrawlState:
    """State management for the crawler"""
    visited_urls: Set[str] = field(default_factory=set)
    queued_urls: deque = field(default_factory=deque)
    failed_urls: Set[str] = field(default_factory=set)
    scraped_content: List[Dict] = field(default_factory=list)
    pages_scraped: int = 0

class URLProcessor:
    """Handles URL processing, normalization, and filtering"""

    def __init__(self, base_domain: str):
        self.base_domain = base_domain
        self.base_netloc = urlparse(base_domain).netloc

    def normalize_url(self, url: str, base_url: str) -> Optional[str]:
        """Normalize and clean URL"""
        try:
            # First, clean any extra text that might be appended to the URL
            # This handles cases like 'https://example.com "Link Text"'
            url = url.strip()

            # Split on whitespace and take only the first part (the actual URL)
            url_parts = url.split()
            if url_parts:
                url = url_parts[0]

            # Remove any quotes that might wrap the URL
            url = url.strip('"\'')

            # Handle relative URLs
            if url.startswith('//'):
                url = 'https:' + url
            elif url.startswith('/'):
                url = urljoin(base_url, url)
            elif not url.startswith(('http://', 'https://')):
                url = urljoin(base_url, url)

            # Parse and clean URL
            parsed = urlparse(url)

            # Validate that we have a proper scheme and netloc
            if not parsed.scheme or not parsed.netloc:
                logger.warning(f"Invalid URL structure: {url}")
                return None

            # Remove fragments and common tracking parameters
            query_params = []
            if parsed.query:
                for param in parsed.query.split('&'):
                    if '=' in param:
                        key, value = param.split('=', 1)
                        # Skip common tracking parameters
                        if key.lower() not in ['utm_source', 'utm_medium', 'utm_campaign',
                                             'utm_content', 'utm_term', 'fbclid', 'gclid']:
                            query_params.append(param)

            # Reconstruct URL without fragment and tracking params
            clean_url = urlunparse((
                parsed.scheme,
                parsed.netloc,
                parsed.path.rstrip('/') or '/',
                parsed.params,
                '&'.join(query_params),
                ''  # Remove fragment
            ))

            # Final validation - ensure the URL looks correct
            if not clean_url.startswith(('http://', 'https://')):
                logger.warning(f"URL doesn't start with http/https after normalization: {clean_url}")
                return None

            return clean_url

        except Exception as e:
            logger.warning(f"Failed to normalize URL {url}: {e}")
            return None

    def is_internal_url(self, url: str) -> bool:
        """Check if URL belongs to the same domain"""
        try:
            parsed = urlparse(url)
            return parsed.netloc == self.base_netloc or parsed.netloc.endswith(f'.{self.base_netloc}')
        except:
            return False

    def is_valid_page_url(self, url: str) -> bool:
        """Check if URL points to a valid web page (not media files, etc.)"""
        try:
            parsed = urlparse(url)
            path = parsed.path.lower()

            # Skip common media file extensions
            media_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx',
                              '.xls', '.xlsx', '.zip', '.rar', '.mp4', '.mp3', '.avi',
                              '.mov', '.wmv', '.css', '.js', '.ico', '.svg'}

            if any(path.endswith(ext) for ext in media_extensions):
                return False

            # Skip common non-content paths
            skip_patterns = [
                r'/wp-admin/', r'/admin/', r'/login', r'/register', r'/cart',
                r'/checkout', r'/search', r'/tag/', r'/category/',
                r'/feed/', r'/rss', r'/sitemap', r'/robots\.txt'
            ]

            if any(re.search(pattern, path) for pattern in skip_patterns):
                return False

            return True

        except:
            return False

class ScrapeNinjaClient:
    """Async client for ScrapeNinja API"""

    def __init__(self, api_key: str, config: CrawlConfig):
        self.api_key = api_key
        self.config = config
        self.base_url = "https://scrapeninja.apiroad.net/scrape"

    async def scrape_url(self, session: aiohttp.ClientSession, url: str) -> Optional[Dict]:
        """Scrape a single URL using ScrapeNinja API"""
        headers = {
            "Content-Type": "application/json",
            "x-apiroad-key": self.api_key
        }

        # Validate and clean URL
        if not url or not url.startswith(('http://', 'https://')):
            logger.error(f"Invalid URL format: {url}")
            return {
                'url': url,
                'content': '',
                'links': [],
                'status': 'failed',
                'timestamp': time.time(),
                'error': 'Invalid URL format'
            }

        # Clean URL - remove fragments and normalize
        from urllib.parse import urlparse, urlunparse
        parsed = urlparse(url)
        clean_url = urlunparse((
            parsed.scheme,
            parsed.netloc,
            parsed.path,
            parsed.params,
            parsed.query,
            ''  # Remove fragment
        ))

        payload = {
            "url": clean_url,
            "method": "GET",
            "retryNum": min(max(1, self.config.retries), 5),  # Ensure retryNum is between 1-5
            "geo": self.config.geo,
            "extractorPreset": self.config.extractor_preset
        }

        # Validate payload values
        if not isinstance(payload["retryNum"], int) or payload["retryNum"] < 1:
            payload["retryNum"] = 3

        if payload["geo"] not in ["us", "eu", "asia"]:
            payload["geo"] = "us"

        if payload["extractorPreset"] not in ["markdown", "text", "html"]:
            payload["extractorPreset"] = "markdown"

        for attempt in range(self.config.retries):
            try:
                logger.info(f"Scraping URL: {url} (attempt {attempt + 1})")

                async with session.post(
                    self.base_url,
                    json=payload,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=self.config.timeout)
                ) as response:

                    if response.status == 200:
                        result = await response.json()

                        # Extract content and links
                        content = ""
                        links = []

                        if 'extractor' in result and 'result' in result['extractor']:
                            extractor_result = result['extractor']['result']
                            if 'markdown' in extractor_result:
                                content = extractor_result['markdown']

                            # Extract links from the content
                            if 'links' in extractor_result:
                                # ScrapeNinja provides links directly
                                raw_links = extractor_result['links']
                                # Clean and validate each link
                                for link in raw_links:
                                    if isinstance(link, str):
                                        # Clean any extra text from the link
                                        clean_link = link.strip().split()[0] if link.strip() else ""
                                        if clean_link and (clean_link.startswith('http://') or clean_link.startswith('https://')):
                                            links.append(clean_link)
                                    elif isinstance(link, dict) and 'url' in link:
                                        # Handle structured link objects
                                        clean_link = link['url'].strip()
                                        if clean_link and (clean_link.startswith('http://') or clean_link.startswith('https://')):
                                            links.append(clean_link)
                            else:
                                # Extract links from markdown content as fallback
                                link_pattern = r'\[([^\]]*)\]\(([^)]+)\)'
                                markdown_links = re.findall(link_pattern, content)
                                for link_text, link_url in markdown_links:
                                    # Clean the URL and remove any extra text
                                    clean_url = link_url.strip().split()[0] if link_url.strip() else ""
                                    if clean_url and (clean_url.startswith('http://') or clean_url.startswith('https://')):
                                        links.append(clean_url)

                        return {
                            'url': url,
                            'content': content,
                            'links': links,
                            'status': 'success',
                            'timestamp': time.time()
                        }

                    elif response.status == 422:
                        # Handle validation errors specifically
                        error_text = await response.text()
                        logger.error(f"HTTP 422 Validation Error for {url}")
                        logger.error(f"Request payload: {payload}")
                        logger.error(f"Response: {error_text}")

                        # Try to parse error details
                        try:
                            error_data = await response.json()
                            if 'message' in error_data:
                                logger.error(f"Error message: {error_data['message']}")
                            if 'errors' in error_data:
                                logger.error(f"Validation errors: {error_data['errors']}")
                        except:
                            pass

                        # Don't retry 422 errors as they're usually permanent
                        break

                    elif response.status == 429:
                        # Rate limiting - wait longer before retry
                        error_text = await response.text()
                        logger.warning(f"HTTP 429 Rate Limited for {url}: {error_text}")
                        await asyncio.sleep(5)  # Wait 5 seconds for rate limiting

                    elif response.status >= 500:
                        # Server errors - retry with backoff
                        error_text = await response.text()
                        logger.warning(f"HTTP {response.status} Server Error for {url}: {error_text}")

                    else:
                        # Other client errors
                        error_text = await response.text()
                        logger.warning(f"HTTP {response.status} for {url}: {error_text}")

                        # Don't retry 4xx errors (except 429) as they're usually permanent
                        if 400 <= response.status < 500 and response.status != 429:
                            break

            except asyncio.TimeoutError:
                logger.warning(f"Timeout scraping {url} (attempt {attempt + 1})")
            except Exception as e:
                logger.error(f"Error scraping {url} (attempt {attempt + 1}): {e}")

            if attempt < self.config.retries - 1:
                await asyncio.sleep(2 ** attempt)  # Exponential backoff

        return {
            'url': url,
            'content': '',
            'links': [],
            'status': 'failed',
            'timestamp': time.time()
        }

class AsyncWebCrawler:
    """Main asynchronous web crawler"""

    def __init__(self, config: CrawlConfig = None):
        self.config = config or CrawlConfig()
        self.scrapeninja_api_key = os.getenv('SCRAPENINJA_API_KEY')

        if not self.scrapeninja_api_key:
            raise ValueError("SCRAPENINJA_API_KEY not found in environment variables")

    async def crawl_website(self, start_url: str, output_dir: str = "scraped_data") -> Dict:
        """Crawl a website starting from the given URL"""

        # Initialize components
        url_processor = URLProcessor(start_url)
        scraper = ScrapeNinjaClient(self.scrapeninja_api_key, self.config)
        state = CrawlState()

        # Normalize start URL and add to queue
        normalized_start = url_processor.normalize_url(start_url, start_url)
        if not normalized_start:
            raise ValueError(f"Invalid start URL: {start_url}")

        state.queued_urls.append((normalized_start, 0))  # (url, depth)

        # Create output directory
        os.makedirs(output_dir, exist_ok=True)

        # Start crawling
        async with aiohttp.ClientSession() as session:
            while state.queued_urls and state.pages_scraped < self.config.max_pages:
                current_url, depth = state.queued_urls.popleft()

                # Skip if already visited or depth exceeded
                if current_url in state.visited_urls or depth > self.config.max_depth:
                    continue

                state.visited_urls.add(current_url)

                # Scrape the page
                result = await scraper.scrape_url(session, current_url)

                if result['status'] == 'success':
                    state.scraped_content.append(result)
                    state.pages_scraped += 1

                    logger.info(f"Successfully scraped {current_url} ({state.pages_scraped}/{self.config.max_pages})")

                    # Process links if we haven't reached max depth
                    if depth < self.config.max_depth:
                        logger.debug(f"Processing {len(result['links'])} links from {current_url}")
                        for i, link in enumerate(result['links']):
                            # Debug: log the raw link before processing
                            if '"' in link or ' ' in link.strip():
                                logger.warning(f"Raw link {i+1} contains suspicious characters: '{link}'")

                            normalized_link = url_processor.normalize_url(link, current_url)

                            if normalized_link:
                                if (normalized_link not in state.visited_urls and
                                    url_processor.is_internal_url(normalized_link) and
                                    url_processor.is_valid_page_url(normalized_link)):

                                    logger.debug(f"Adding to queue: {normalized_link}")
                                    state.queued_urls.append((normalized_link, depth + 1))
                                else:
                                    logger.debug(f"Skipping link (external/invalid/visited): {normalized_link}")
                            else:
                                logger.debug(f"Failed to normalize link: '{link}'")

                else:
                    state.failed_urls.add(current_url)
                    logger.warning(f"Failed to scrape {current_url}")

                # Rate limiting
                await asyncio.sleep(self.config.delay_between_requests)

        # Save results
        timestamp = int(time.time())
        domain = urlparse(start_url).netloc
        output_file = os.path.join(output_dir, f"{domain}_{timestamp}.json")

        crawl_results = {
            'start_url': start_url,
            'timestamp': timestamp,
            'config': {
                'max_pages': self.config.max_pages,
                'max_depth': self.config.max_depth,
                'pages_scraped': state.pages_scraped
            },
            'visited_urls': list(state.visited_urls),
            'failed_urls': list(state.failed_urls),
            'scraped_content': state.scraped_content
        }

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(crawl_results, f, indent=2, ensure_ascii=False)

        logger.info(f"Crawl completed. Results saved to {output_file}")
        return crawl_results
